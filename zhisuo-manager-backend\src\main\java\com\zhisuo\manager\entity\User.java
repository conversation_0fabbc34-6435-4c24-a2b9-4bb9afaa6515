package com.zhisuo.manager.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
@TableName("users")
public class User {
    
    /**
     * 用户ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String userId;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 密码哈希值
     */
    private String passwordHash;
    
    /**
     * 密码盐值
     */
    private String passwordSalt;
    
    /**
     * 用户昵称
     */
    private String nickname;
    
    /**
     * 头像URL
     */
    private String avatar;
    
    /**
     * 会员等级(0:普通,1:黄金,2:铂金等)
     */
    private Integer memberLevel;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 状态(1:正常,0:禁用)
     */
    private Integer status;
}
