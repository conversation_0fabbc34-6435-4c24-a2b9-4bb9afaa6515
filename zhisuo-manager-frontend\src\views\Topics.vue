<template>
  <div class="topics-page">
    <div class="page-header">
      <h2>热点话题</h2>
      <p>管理热点话题内容</p>
    </div>
    
    <div class="content-card">
      <div class="coming-soon">
        <el-icon size="64" color="#ccc"><ChatDotRound /></el-icon>
        <h3>热点话题功能开发中...</h3>
        <p>敬请期待</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 热点话题页面
</script>

<style lang="scss" scoped>
.topics-page {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }
    
    p {
      color: #666;
      margin: 0;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 80px 20px;
    
    h3 {
      margin: 20px 0 10px 0;
      color: #666;
    }
    
    p {
      color: #999;
      margin: 0;
    }
  }
}
</style>
