<template>
  <div class="users-page">
    <div class="page-header">
      <h2>用户管理</h2>
      <p>管理系统用户信息和状态</p>
    </div>
    
    <!-- 搜索和操作区域 -->
    <div class="content-card">
      <el-form :model="queryForm" inline class="search-form">
        <el-form-item label="手机号">
          <el-input
            v-model="queryForm.phone"
            placeholder="请输入手机号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="昵称">
          <el-input
            v-model="queryForm.nickname"
            placeholder="请输入昵称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="会员等级">
          <el-select v-model="queryForm.memberLevel" placeholder="请选择" clearable style="width: 150px">
            <el-option label="普通会员" :value="0" />
            <el-option label="黄金会员" :value="1" />
            <el-option label="铂金会员" :value="2" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="queryForm.status" placeholder="请选择" clearable style="width: 120px">
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 用户列表 -->
    <div class="content-card">
      <div class="table-header">
        <div class="table-title">用户列表</div>
        <div class="table-actions">
          <el-button type="primary" @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <el-table
        v-loading="loading"
        :data="userList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="userId" label="用户ID" width="120" show-overflow-tooltip />
        
        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="nickname" label="昵称" width="120" show-overflow-tooltip />
        
        <el-table-column label="会员等级" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.memberLevel === 0 ? 'info' : row.memberLevel === 1 ? 'warning' : 'success'"
              size="small"
            >
              {{ row.memberLevelName }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
              {{ row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="favoriteCount" label="收藏数" width="80" />
        <el-table-column prop="likeCount" label="点赞数" width="80" />
        <el-table-column prop="commentCount" label="评论数" width="80" />
        
        <el-table-column prop="createTime" label="注册时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="lastLoginTime" label="最后登录" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.lastLoginTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">
              详情
            </el-button>
            <el-button
              :type="row.status === 1 ? 'warning' : 'success'"
              size="small"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="用户详情"
      width="600px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ currentUser.userId }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ currentUser.phone }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ currentUser.nickname }}</el-descriptions-item>
          <el-descriptions-item label="会员等级">{{ currentUser.memberLevelName }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentUser.status === 1 ? 'success' : 'danger'">
              {{ currentUser.statusName }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="收藏数">{{ currentUser.favoriteCount }}</el-descriptions-item>
          <el-descriptions-item label="点赞数">{{ currentUser.likeCount }}</el-descriptions-item>
          <el-descriptions-item label="评论数">{{ currentUser.commentCount }}</el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ formatDateTime(currentUser.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">{{ formatDateTime(currentUser.lastLoginTime) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const userList = ref([])
const detailDialogVisible = ref(false)
const currentUser = ref(null)

// 查询表单
const queryForm = reactive({
  phone: '',
  nickname: '',
  memberLevel: null,
  status: null
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchUserList()
}

// 重置
const handleReset = () => {
  Object.keys(queryForm).forEach(key => {
    queryForm[key] = key === 'status' || key === 'memberLevel' ? null : ''
  })
  pagination.current = 1
  fetchUserList()
}

// 刷新
const handleRefresh = () => {
  fetchUserList()
}

// 查看详情
const handleViewDetail = async (user) => {
  try {
    // 这里应该调用API获取用户详情
    currentUser.value = user
    detailDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取用户详情失败')
  }
}

// 关闭详情对话框
const handleCloseDetail = () => {
  detailDialogVisible.value = false
  currentUser.value = null
}

// 切换用户状态
const handleToggleStatus = async (user) => {
  const action = user.status === 1 ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 ${user.nickname} 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用API更新用户状态
    user.status = user.status === 1 ? 0 : 1
    user.statusName = user.status === 1 ? '正常' : '禁用'
    
    ElMessage.success(`${action}成功`)
  } catch (error) {
    // 用户取消操作
  }
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchUserList()
}

// 当前页改变
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchUserList()
}

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    const mockData = {
      records: [
        {
          userId: 'user001',
          phone: '13800138001',
          nickname: '张三',
          avatar: '',
          memberLevel: 1,
          memberLevelName: '黄金会员',
          status: 1,
          statusName: '正常',
          favoriteCount: 25,
          likeCount: 156,
          commentCount: 89,
          createTime: '2024-01-15T10:30:00',
          lastLoginTime: '2024-01-20T15:45:00'
        },
        {
          userId: 'user002',
          phone: '13800138002',
          nickname: '李四',
          avatar: '',
          memberLevel: 0,
          memberLevelName: '普通会员',
          status: 1,
          statusName: '正常',
          favoriteCount: 12,
          likeCount: 78,
          commentCount: 34,
          createTime: '2024-01-16T09:20:00',
          lastLoginTime: '2024-01-19T14:30:00'
        }
      ],
      total: 2,
      current: pagination.current,
      size: pagination.size
    }
    
    userList.value = mockData.records
    pagination.total = mockData.total
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchUserList()
})
</script>

<style lang="scss" scoped>
.users-page {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }
    
    p {
      color: #666;
      margin: 0;
    }
  }
  
  .search-form {
    .el-form-item {
      margin-bottom: 16px;
    }
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .user-detail {
    .el-descriptions {
      margin-top: 20px;
    }
  }
}
</style>
