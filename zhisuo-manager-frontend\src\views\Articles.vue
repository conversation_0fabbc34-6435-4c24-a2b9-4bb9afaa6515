<template>
  <div class="articles-page">
    <div class="page-header">
      <h2>文章管理</h2>
      <p>管理系统文章内容</p>
    </div>
    
    <div class="content-card">
      <div class="coming-soon">
        <el-icon size="64" color="#ccc"><Document /></el-icon>
        <h3>文章管理功能开发中...</h3>
        <p>敬请期待</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 文章管理页面
</script>

<style lang="scss" scoped>
.articles-page {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }
    
    p {
      color: #666;
      margin: 0;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 80px 20px;
    
    h3 {
      margin: 20px 0 10px 0;
      color: #666;
    }
    
    p {
      color: #999;
      margin: 0;
    }
  }
}
</style>
