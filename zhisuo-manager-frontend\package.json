{"name": "<PERSON><PERSON><PERSON>-manager-frontend", "version": "1.0.0", "description": "智索管理系统前端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "element-plus": "^2.4.0", "axios": "^1.6.0", "echarts": "^5.4.0", "@element-plus/icons-vue": "^2.3.0", "nprogress": "^0.2.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "sass": "^1.69.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.0", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.1.0", "unplugin-auto-import": "^0.17.0", "unplugin-vue-components": "^0.26.0"}, "keywords": ["vue3", "vite", "element-plus", "admin", "management"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT"}