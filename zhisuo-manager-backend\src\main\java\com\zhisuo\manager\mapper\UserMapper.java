package com.zhisuo.manager.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.manager.dto.UserQueryRequest;
import com.zhisuo.manager.entity.User;
import com.zhisuo.manager.vo.UserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户Mapper
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 分页查询用户列表
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 用户列表
     */
    IPage<UserVO> selectUserPage(Page<UserVO> page, @Param("request") UserQueryRequest request);
    
    /**
     * 根据用户ID获取用户详情
     *
     * @param userId 用户ID
     * @return 用户详情
     */
    UserVO selectUserDetail(@Param("userId") String userId);
}
