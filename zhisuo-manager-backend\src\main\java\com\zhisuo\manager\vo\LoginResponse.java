package com.zhisuo.manager.vo;

import lombok.Data;

/**
 * 登录响应VO
 */
@Data
public class LoginResponse {
    
    /**
     * 访问令牌
     */
    private String accessToken;
    
    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";
    
    /**
     * 过期时间（秒）
     */
    private Long expiresIn;
    
    /**
     * 管理员信息
     */
    private AdminInfo adminInfo;
    
    @Data
    public static class AdminInfo {
        /**
         * 管理员ID
         */
        private String adminId;
        
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 真实姓名
         */
        private String realName;
        
        /**
         * 邮箱
         */
        private String email;
        
        /**
         * 头像URL
         */
        private String avatar;
        
        /**
         * 角色
         */
        private String role;
    }
}
