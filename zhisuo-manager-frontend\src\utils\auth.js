import Cookies from 'js-cookie'

const TokenKey = 'zhisuo_manager_token'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token, { expires: 7 }) // 7天过期
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

// 检查是否有权限
export function hasPermission(permission) {
  // 这里可以根据实际需求实现权限检查逻辑
  return true
}

// 检查是否有角色
export function hasRole(role) {
  // 这里可以根据实际需求实现角色检查逻辑
  return true
}
