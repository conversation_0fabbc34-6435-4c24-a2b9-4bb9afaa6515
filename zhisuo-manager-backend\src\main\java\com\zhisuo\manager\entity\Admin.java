package com.zhisuo.manager.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理员实体类
 */
@Data
@TableName("admin")
public class Admin {
    
    /**
     * 管理员ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String adminId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码哈希值
     */
    private String passwordHash;
    
    /**
     * 密码盐值
     */
    private String passwordSalt;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 头像URL
     */
    private String avatar;
    
    /**
     * 角色(admin:超级管理员, manager:普通管理员)
     */
    private String role;
    
    /**
     * 状态(1:正常, 0:禁用)
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 最后登录IP
     */
    private String lastLoginIp;
    
    /**
     * 备注
     */
    private String remark;
}
