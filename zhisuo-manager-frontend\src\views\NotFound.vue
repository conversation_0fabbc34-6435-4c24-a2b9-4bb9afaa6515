<template>
  <div class="not-found">
    <div class="not-found-content">
      <div class="error-code">404</div>
      <h2>页面不存在</h2>
      <p>抱歉，您访问的页面不存在或已被删除</p>
      <el-button type="primary" @click="goHome">
        <el-icon><HomeFilled /></el-icon>
        返回首页
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}
</script>

<style lang="scss" scoped>
.not-found {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  
  .not-found-content {
    text-align: center;
    
    .error-code {
      font-size: 120px;
      font-weight: 600;
      color: #1890ff;
      line-height: 1;
      margin-bottom: 20px;
    }
    
    h2 {
      font-size: 24px;
      color: #333;
      margin: 0 0 16px 0;
    }
    
    p {
      font-size: 16px;
      color: #666;
      margin: 0 0 32px 0;
    }
    
    .el-button {
      .el-icon {
        margin-right: 8px;
      }
    }
  }
}
</style>
