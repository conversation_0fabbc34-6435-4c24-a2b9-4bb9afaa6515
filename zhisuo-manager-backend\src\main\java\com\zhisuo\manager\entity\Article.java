package com.zhisuo.manager.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文章实体类
 */
@Data
@TableName("articles")
public class Article {
    
    /**
     * 文章ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String articleId;
    
    /**
     * 文章标题
     */
    private String title;
    
    /**
     * 文章描述
     */
    private String description;
    
    /**
     * 文章内容
     */
    private String content;
    
    /**
     * 封面图URL
     */
    private String coverImage;
    
    /**
     * 内容来源
     */
    private String source;
    
    /**
     * 来源URL
     */
    private String sourceUrl;
    
    /**
     * 图标URL
     */
    private String iconUrl;
    
    /**
     * 阅读量
     */
    private Integer viewCount;
    
    /**
     * 评论量
     */
    private Integer commentCount;
    
    /**
     * 点赞量
     */
    private Integer likeCount;
    
    /**
     * 是否已读
     */
    private Integer isRead;
    
    /**
     * 是否已收藏
     */
    private Integer isFavorite;
    
    /**
     * 收集时间
     */
    private LocalDateTime collectTime;
    
    /**
     * 发布时间
     */
    private LocalDateTime publishTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 状态(1:已发布,0:草稿,-1:已删除)
     */
    private Integer status;
}
