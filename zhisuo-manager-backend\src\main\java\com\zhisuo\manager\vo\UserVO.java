package com.zhisuo.manager.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户响应VO
 */
@Data
public class UserVO {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 用户昵称
     */
    private String nickname;
    
    /**
     * 头像URL
     */
    private String avatar;
    
    /**
     * 会员等级
     */
    private Integer memberLevel;
    
    /**
     * 会员等级名称
     */
    private String memberLevelName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 状态(1:正常,0:禁用)
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 收藏数量
     */
    private Long favoriteCount;
    
    /**
     * 点赞数量
     */
    private Long likeCount;
    
    /**
     * 评论数量
     */
    private Long commentCount;
}
