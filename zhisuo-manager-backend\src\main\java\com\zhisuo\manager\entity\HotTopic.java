package com.zhisuo.manager.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 热点话题实体类
 */
@Data
@TableName("hot_topics")
public class HotTopic {
    
    /**
     * 话题ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String topicId;
    
    /**
     * 话题标题
     */
    private String title;
    
    /**
     * 话题描述
     */
    private String description;
    
    /**
     * 来源平台
     */
    private String source;
    
    /**
     * 来源URL
     */
    private String sourceUrl;
    
    /**
     * 热度值
     */
    private String hotValue;
    
    /**
     * 阅读量
     */
    private Integer viewCount;
    
    /**
     * 搜索量
     */
    private Integer searchCount;
    
    /**
     * 趋势(1:上升,0:持平,-1:下降)
     */
    private Integer trend;
    
    /**
     * 热榜排名
     */
    private Integer rank;
    
    /**
     * 是否已读
     */
    private Integer isRead;
    
    /**
     * 是否已收藏
     */
    private Integer isFavorite;
    
    /**
     * 收集时间
     */
    private LocalDateTime collectTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 状态(1:显示,0:不显示)
     */
    private Integer status;
}
