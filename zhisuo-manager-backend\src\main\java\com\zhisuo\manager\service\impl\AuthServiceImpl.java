package com.zhisuo.manager.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zhisuo.manager.common.JwtUtil;
import com.zhisuo.manager.dto.LoginRequest;
import com.zhisuo.manager.entity.Admin;
import com.zhisuo.manager.mapper.AdminMapper;
import com.zhisuo.manager.service.AuthService;
import com.zhisuo.manager.vo.LoginResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现类
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {
    
    @Autowired
    private AdminMapper adminMapper;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    @Value("${jwt.expiration}")
    private Long jwtExpiration;
    
    private static final String TOKEN_PREFIX = "manager:token:";
    
    @Override
    public LoginResponse login(LoginRequest request) {
        String username = request.getUsername();
        String password = request.getPassword();
        
        // 查询管理员信息
        Admin admin = getAdminByUsername(username);
        if (admin == null) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 检查账户状态
        if (admin.getStatus() == 0) {
            throw new RuntimeException("账户已被禁用");
        }
        
        // 验证密码
        if (!BCrypt.checkpw(password, admin.getPasswordHash())) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 生成JWT Token
        String token = jwtUtil.generateToken(username, admin.getAdminId());
        
        // 将Token存储到Redis
        String tokenKey = TOKEN_PREFIX + admin.getAdminId();
        redisTemplate.opsForValue().set(tokenKey, token, jwtExpiration, TimeUnit.MILLISECONDS);
        
        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setAccessToken(token);
        response.setExpiresIn(jwtExpiration / 1000);
        
        LoginResponse.AdminInfo adminInfo = new LoginResponse.AdminInfo();
        adminInfo.setAdminId(admin.getAdminId());
        adminInfo.setUsername(admin.getUsername());
        adminInfo.setRealName(admin.getRealName());
        adminInfo.setEmail(admin.getEmail());
        adminInfo.setAvatar(admin.getAvatar());
        adminInfo.setRole(admin.getRole());
        response.setAdminInfo(adminInfo);
        
        log.info("管理员[{}]登录成功", username);
        return response;
    }
    
    @Override
    public void logout(String token) {
        try {
            String userId = jwtUtil.getUserIdFromToken(token);
            String tokenKey = TOKEN_PREFIX + userId;
            redisTemplate.delete(tokenKey);
            log.info("管理员[{}]登出成功", userId);
        } catch (Exception e) {
            log.error("登出失败", e);
        }
    }
    
    @Override
    public Admin getAdminByUsername(String username) {
        if (StrUtil.isBlank(username)) {
            return null;
        }
        
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Admin::getUsername, username);
        return adminMapper.selectOne(queryWrapper);
    }
    
    @Override
    public Admin getAdminById(String adminId) {
        if (StrUtil.isBlank(adminId)) {
            return null;
        }
        return adminMapper.selectById(adminId);
    }
    
    @Override
    public void updateLastLogin(String adminId, String loginIp) {
        LambdaUpdateWrapper<Admin> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Admin::getAdminId, adminId)
                .set(Admin::getLastLoginTime, LocalDateTime.now())
                .set(Admin::getLastLoginIp, loginIp);
        adminMapper.update(null, updateWrapper);
    }
}
