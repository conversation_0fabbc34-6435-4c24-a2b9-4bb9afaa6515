# 智索管理系统前端

基于 Vue 3 + Vite + Element Plus 构建的现代化管理系统前端。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 下一代前端构建工具
- **Element Plus** - 基于 Vue 3 的组件库
- **Pinia** - Vue 状态管理库
- **Vue Router** - Vue 官方路由管理器
- **Axios** - HTTP 客户端
- **ECharts** - 数据可视化图表库
- **Sass** - CSS 预处理器

## 功能特性

- 🔐 **用户认证** - JWT Token 认证，路由守卫
- 📊 **数据仪表盘** - 数据概览，图表展示
- 👥 **用户管理** - 用户列表，状态管理，详情查看
- 📝 **内容管理** - 文章管理，热点话题，标签分类
- 📈 **数据统计** - 用户统计，内容分析，访问统计
- ⚙️ **系统管理** - 管理员管理，操作日志，系统配置
- 📱 **响应式设计** - 适配桌面端和移动端
- 🎨 **现代化UI** - 简洁美观的界面设计

## 项目结构

```
src/
├── api/           # API 接口
├── assets/        # 静态资源
├── components/    # 公共组件
├── router/        # 路由配置
├── stores/        # 状态管理
├── styles/        # 样式文件
├── utils/         # 工具函数
├── views/         # 页面组件
├── App.vue        # 根组件
└── main.js        # 入口文件
```

## 开发指南

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 默认账号

- 用户名：admin
- 密码：admin123

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License
