<template>
  <div class="dashboard">
    <div class="page-header">
      <h2>仪表盘</h2>
      <p>欢迎使用智索管理系统</p>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card user-stat">
          <div class="stat-icon">
            <el-icon size="32"><User /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-title">总用户数</div>
            <div class="stat-value">{{ stats.totalUsers || 0 }}</div>
            <div class="stat-desc">
              <span class="trend-up">+{{ stats.todayNewUsers || 0 }}</span>
              今日新增
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card article-stat">
          <div class="stat-icon">
            <el-icon size="32"><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-title">文章总数</div>
            <div class="stat-value">{{ stats.totalArticles || 0 }}</div>
            <div class="stat-desc">
              <span class="trend-up">+{{ stats.todayArticles || 0 }}</span>
              今日新增
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card topic-stat">
          <div class="stat-icon">
            <el-icon size="32"><ChatDotRound /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-title">热点话题</div>
            <div class="stat-value">{{ stats.totalTopics || 0 }}</div>
            <div class="stat-desc">
              <span class="trend-up">+{{ stats.todayTopics || 0 }}</span>
              今日新增
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card view-stat">
          <div class="stat-icon">
            <el-icon size="32"><View /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-title">总访问量</div>
            <div class="stat-value">{{ formatNumber(stats.totalViews) || 0 }}</div>
            <div class="stat-desc">
              <span class="trend-up">+{{ formatNumber(stats.todayViews) || 0 }}</span>
              今日访问
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :lg="12">
        <div class="content-card">
          <div class="card-header">
            <h3>用户增长趋势</h3>
          </div>
          <div class="chart-container" ref="userChartRef"></div>
        </div>
      </el-col>
      
      <el-col :xs="24" :lg="12">
        <div class="content-card">
          <div class="card-header">
            <h3>内容分布</h3>
          </div>
          <div class="chart-container" ref="contentChartRef"></div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 快捷操作 -->
    <div class="content-card">
      <div class="card-header">
        <h3>快捷操作</h3>
      </div>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-button type="primary" size="large" class="quick-btn" @click="$router.push('/users')">
            <el-icon><User /></el-icon>
            用户管理
          </el-button>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-button type="success" size="large" class="quick-btn" @click="$router.push('/articles')">
            <el-icon><Document /></el-icon>
            文章管理
          </el-button>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-button type="warning" size="large" class="quick-btn" @click="$router.push('/topics')">
            <el-icon><ChatDotRound /></el-icon>
            热点话题
          </el-button>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-button type="info" size="large" class="quick-btn" @click="$router.push('/statistics')">
            <el-icon><TrendCharts /></el-icon>
            数据统计
          </el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const userChartRef = ref()
const contentChartRef = ref()

// 模拟统计数据
const stats = ref({
  totalUsers: 1234,
  todayNewUsers: 56,
  totalArticles: 2345,
  todayArticles: 23,
  totalTopics: 456,
  todayTopics: 12,
  totalViews: 123456,
  todayViews: 3456
})

// 格式化数字
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 初始化用户增长图表
const initUserChart = () => {
  const chart = echarts.init(userChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [120, 200, 150, 80, 70, 110, 130],
      type: 'line',
      smooth: true,
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
          { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
        ])
      },
      lineStyle: {
        color: '#1890ff'
      }
    }]
  }
  chart.setOption(option)
  
  // 响应式
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 初始化内容分布图表
const initContentChart = () => {
  const chart = echarts.init(contentChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'pie',
      radius: '60%',
      data: [
        { value: 1048, name: '科技资讯' },
        { value: 735, name: '财经新闻' },
        { value: 580, name: '社会热点' },
        { value: 484, name: '娱乐八卦' },
        { value: 300, name: '体育新闻' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  chart.setOption(option)
  
  // 响应式
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

onMounted(async () => {
  await nextTick()
  initUserChart()
  initContentChart()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }
    
    p {
      color: #666;
      margin: 0;
    }
  }
  
  .stats-row {
    margin-bottom: 24px;
  }
  
  .stat-card {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
    
    .stat-icon {
      width: 64px;
      height: 64px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
    }
    
    .stat-content {
      flex: 1;
      
      .stat-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }
      
      .stat-value {
        font-size: 28px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }
      
      .stat-desc {
        font-size: 12px;
        color: #999;
        
        .trend-up {
          color: #52c41a;
          font-weight: 500;
        }
      }
    }
    
    &.user-stat .stat-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    &.article-stat .stat-icon {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    &.topic-stat .stat-icon {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    &.view-stat .stat-icon {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
  }
  
  .charts-row {
    margin-bottom: 24px;
  }
  
  .content-card {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    
    .card-header {
      margin-bottom: 20px;
      
      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }
    }
    
    .chart-container {
      height: 300px;
    }
  }
  
  .quick-btn {
    width: 100%;
    height: 60px;
    font-size: 16px;
    margin-bottom: 16px;
    
    .el-icon {
      margin-right: 8px;
    }
  }
}

@media (max-width: 768px) {
  .stat-card {
    padding: 16px;
    
    .stat-icon {
      width: 48px;
      height: 48px;
    }
    
    .stat-content .stat-value {
      font-size: 24px;
    }
  }
  
  .content-card {
    padding: 16px;
    
    .chart-container {
      height: 250px;
    }
  }
}
</style>
