package com.zhisuo.manager.service;

import com.zhisuo.manager.dto.LoginRequest;
import com.zhisuo.manager.entity.Admin;
import com.zhisuo.manager.vo.LoginResponse;

/**
 * 认证服务接口
 */
public interface AuthService {
    
    /**
     * 管理员登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest request);
    
    /**
     * 管理员登出
     *
     * @param token 令牌
     */
    void logout(String token);
    
    /**
     * 根据用户名获取管理员信息
     *
     * @param username 用户名
     * @return 管理员信息
     */
    Admin getAdminByUsername(String username);
    
    /**
     * 根据ID获取管理员信息
     *
     * @param adminId 管理员ID
     * @return 管理员信息
     */
    Admin getAdminById(String adminId);
    
    /**
     * 更新最后登录时间和IP
     *
     * @param adminId 管理员ID
     * @param loginIp 登录IP
     */
    void updateLastLogin(String adminId, String loginIp);
}
