# 智索管理系统后端

## 项目介绍

智索管理系统后端是基于Spring Boot开发的后台管理系统，为智索APP提供管理功能，包括用户管理、内容管理、数据统计等功能。

## 技术栈

- **框架**: Spring Boot 2.7.6
- **安全**: Spring Security + JWT
- **数据库**: MySQL 8.0 + MyBatis-Plus 3.5.3
- **缓存**: Redis
- **工具**: Hutool, FastJSON, Lombok

## 功能模块

### 1. 认证授权模块
- 管理员登录/登出
- JWT Token认证
- 角色权限控制

### 2. 用户管理模块
- 用户列表查询
- 用户详情查看
- 用户状态管理

### 3. 内容管理模块
- 文章管理
- 热点话题管理
- 标签分类管理

### 4. 数据统计模块
- 用户统计
- 内容统计
- 访问统计

### 5. 系统管理模块
- 管理员管理
- 操作日志
- 系统配置

## 快速开始

### 1. 环境要求

- JDK 1.8+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+

### 2. 数据库配置

1. 创建数据库 `zhisuo_app`
2. 执行初始化脚本 `src/main/resources/init.sql`
3. 修改 `application.yml` 中的数据库连接配置

### 3. Redis配置

修改 `application.yml` 中的Redis连接配置

### 4. 启动项目

```bash
mvn clean install
mvn spring-boot:run
```

### 5. 访问地址

- 服务地址: http://localhost:8081/manager
- 健康检查: http://localhost:8081/manager/health

## 默认账户

- 用户名: `admin`
- 密码: `admin123`

## API文档

### 认证接口

#### 登录
```
POST /manager/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

#### 登出
```
POST /manager/auth/logout
Authorization: Bearer {token}
```

#### 获取当前用户信息
```
GET /manager/auth/me
Authorization: Bearer {token}
```

## 项目结构

```
src/main/java/com/zhisuo/manager/
├── ManagerApplication.java          # 启动类
├── config/                          # 配置类
│   └── SecurityConfig.java         # 安全配置
├── controller/                      # 控制器
│   ├── AuthController.java         # 认证控制器
│   └── HealthController.java       # 健康检查
├── service/                         # 服务层
│   ├── AuthService.java            # 认证服务接口
│   └── impl/
│       └── AuthServiceImpl.java    # 认证服务实现
├── mapper/                          # 数据访问层
│   └── AdminMapper.java            # 管理员Mapper
├── entity/                          # 实体类
│   └── Admin.java                  # 管理员实体
├── dto/                            # 数据传输对象
│   └── LoginRequest.java          # 登录请求
├── vo/                             # 视图对象
│   └── LoginResponse.java         # 登录响应
├── common/                         # 通用类
│   ├── Result.java                # 统一响应结果
│   ├── PageResult.java            # 分页响应结果
│   └── JwtUtil.java               # JWT工具类
└── security/                       # 安全相关
    ├── JwtAuthenticationFilter.java    # JWT认证过滤器
    └── JwtAuthenticationEntryPoint.java # JWT认证入口点
```

## 开发规范

1. 所有接口返回统一的Result格式
2. 使用JWT进行身份认证
3. 敏感操作记录操作日志
4. 遵循RESTful API设计规范
5. 使用Lombok简化代码

## 部署说明

1. 修改生产环境配置
2. 打包: `mvn clean package`
3. 运行: `java -jar target/zhisuo-manager-backend-1.0.0.jar`
