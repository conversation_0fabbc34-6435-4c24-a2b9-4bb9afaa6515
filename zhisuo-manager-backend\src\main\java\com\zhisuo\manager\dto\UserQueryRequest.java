package com.zhisuo.manager.dto;

import lombok.Data;

/**
 * 用户查询请求DTO
 */
@Data
public class UserQueryRequest {
    
    /**
     * 当前页码
     */
    private Long current = 1L;
    
    /**
     * 每页大小
     */
    private Long size = 10L;
    
    /**
     * 手机号（模糊查询）
     */
    private String phone;
    
    /**
     * 昵称（模糊查询）
     */
    private String nickname;
    
    /**
     * 会员等级
     */
    private Integer memberLevel;
    
    /**
     * 状态(1:正常,0:禁用)
     */
    private Integer status;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 排序字段
     */
    private String sortField = "createTime";
    
    /**
     * 排序方向(asc/desc)
     */
    private String sortOrder = "desc";
}
