# 智索管理系统前端

## 项目介绍

智索管理系统前端是基于Vue 3 + JavaScript + Element Plus开发的现代化后台管理界面，为智索APP提供完整的管理功能。

## 技术栈

- **框架**: Vue 3.4 +JavaScript
- **构建工具**: Vite 5.0
- **UI组件库**: Element Plus 2.4
- **状态管理**: Pinia 2.1
- **路由**: Vue Router 4.2
- **HTTP客户端**: Axios 1.6
- **图表库**: ECharts 5.4
- **样式**: SCSS

## 功能特性

### 🎨 现代化设计
- 简洁美观的界面设计
- 响应式布局，支持多种屏幕尺寸
- 深色/浅色主题切换
- 流畅的动画效果

### 🔐 安全认证
- JWT Token认证
- 路由权限控制
- 角色权限管理
- 自动登录状态检查

### 📊 数据可视化
- ECharts图表展示
- 实时数据统计
- 多维度数据分析
- 交互式图表操作

### 🛠 开发体验
- JavaScript类型安全
- 组件化开发
- 热重载开发
- ESLint代码规范

## 功能模块

### 1. 仪表盘
- 数据概览
- 用户增长趋势
- 内容统计图表
- 最新动态展示

### 2. 用户管理
- 用户列表查询
- 用户状态管理
- 用户详情查看
- 批量操作

### 3. 内容管理
- 文章管理
- 热点话题管理
- 标签分类管理
- 内容审核

### 4. 数据统计
- 用户统计分析
- 内容访问统计
- 系统使用统计
- 数据导出功能

### 5. 系统管理
- 管理员管理
- 角色权限配置
- 系统配置管理
- 操作日志查看

## 快速开始

### 环境要求

- Node.js 16+
- npm 8+ 或 yarn 1.22+

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发环境

```bash
npm run dev
# 或
yarn dev
```

访问地址: http://localhost:3000

### 生产构建

```bash
npm run build
# 或
yarn build
```

### 代码检查

```bash
npm run lint
# 或
yarn lint
```

## 项目结构

```

```

## 开发规范

### 代码风格
- 使用JavaScript进行类型检查
- 遵循Vue 3 Composition API规范
- 使用ESLint进行代码检查
- 统一的命名规范和注释规范

### 组件开发
- 使用`<script setup>`语法
- 合理拆分组件，提高复用性
- 使用Props和Emits进行组件通信
- 添加必要的类型定义

### 样式规范
- 使用SCSS预处理器
- 采用BEM命名规范
- 响应式设计，适配多种设备
- 统一的颜色和字体规范

## 配置说明

### 环境变量
创建`.env.local`文件配置本地环境变量：

```bash
# API基础地址
VITE_API_BASE_URL=http://localhost:8081/manager

# 应用标题
VITE_APP_TITLE=智索管理系统
```

### 代理配置
开发环境API代理配置在`vite.config.ts`中：

```typescript
server: {
  proxy: {
    '/manager': {
      target: 'http://localhost:8081',
      changeOrigin: true
    }
  }
}
```

## 部署说明

### 构建部署
1. 执行构建命令生成dist目录
2. 将dist目录部署到Web服务器
3. 配置Nginx反向代理

### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /manager {
        proxy_pass http://localhost:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 默认账户

- 用户名: `admin`
- 密码: `admin123`

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 更新日志

### v1.0.0 (2025-08-03)
- 初始版本发布
- 完成基础架构搭建
- 实现登录认证功能
- 完成仪表盘和用户管理模块
