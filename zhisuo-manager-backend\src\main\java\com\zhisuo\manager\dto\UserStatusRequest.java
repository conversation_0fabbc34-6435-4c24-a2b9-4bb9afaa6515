package com.zhisuo.manager.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户状态更新请求DTO
 */
@Data
public class UserStatusRequest {
    
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    /**
     * 状态(1:正常,0:禁用)
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
    
    /**
     * 操作原因
     */
    private String reason;
}
