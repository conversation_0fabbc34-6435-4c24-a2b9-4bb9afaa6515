<template>
  <div class="tags-page">
    <div class="page-header">
      <h2>标签管理</h2>
      <p>管理内容标签分类</p>
    </div>
    
    <div class="content-card">
      <div class="coming-soon">
        <el-icon size="64" color="#ccc"><PriceTag /></el-icon>
        <h3>标签管理功能开发中...</h3>
        <p>敬请期待</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 标签管理页面
</script>

<style lang="scss" scoped>
.tags-page {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }
    
    p {
      color: #666;
      margin: 0;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 80px 20px;
    
    h3 {
      margin: 20px 0 10px 0;
      color: #666;
    }
    
    p {
      color: #999;
      margin: 0;
    }
  }
}
</style>
