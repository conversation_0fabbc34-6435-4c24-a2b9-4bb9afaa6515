import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(getToken())
  const userInfo = ref(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!userInfo.value)

  // 登录
  const login = async (loginForm) => {
    loading.value = true
    try {
      const response = await authApi.login(loginForm)
      const { accessToken, adminInfo } = response.data
      
      token.value = accessToken
      userInfo.value = adminInfo
      setToken(accessToken)
      
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      token.value = null
      userInfo.value = null
      removeToken()
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    const savedToken = getToken()
    if (!savedToken) {
      return false
    }

    try {
      const response = await authApi.getCurrentUser()
      userInfo.value = response.data
      token.value = savedToken
      return true
    } catch (error) {
      console.error('检查认证状态失败:', error)
      removeToken()
      token.value = null
      userInfo.value = null
      return false
    }
  }

  // 更新用户信息
  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
  }

  return {
    token,
    userInfo,
    loading,
    isAuthenticated,
    login,
    logout,
    checkAuth,
    updateUserInfo
  }
})
