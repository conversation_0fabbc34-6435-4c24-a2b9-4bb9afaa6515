package com.zhisuo.manager.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 标签实体类
 */
@Data
@TableName("tags")
public class Tag {
    
    /**
     * 标签ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String tagId;
    
    /**
     * 标签名称
     */
    private String tagName;
    
    /**
     * 标签图标
     */
    private String icon;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 状态(1:启用,0:禁用)
     */
    private Integer status;
}
